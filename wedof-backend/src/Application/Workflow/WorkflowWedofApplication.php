<?php
// src/Application/Workflow/WorkflowWedofApplication.php
namespace App\Application\Workflow;

use App\Application\WedofApplication;
use App\Entity\Application;
use App\Entity\Subscription;
use App\Entity\User;
use App\Exception\WedofBadRequestHttpException;
use App\Library\utils\enums\ApplicationStates;
use App\Library\utils\enums\SubscriptionTrainingTypes;
use App\Library\utils\Tools;
use App\Service\ActivityService;
use App\Service\ApplicationService;
use DateTime;
use Exception;
use Firebase\JWT\JWT;
use RuntimeException;
use Symfony\Component\HttpClient\CurlHttpClient;
use Symfony\Component\Security\Core\Security;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;

class WorkflowWedofApplication extends WedofApplication
{
    protected static string $APP_ID = "workflow";
    protected static array $METHODS = ["auth"];
    private Security $security;
    private const INACTIVE = 'INACTIVE';
    private const ACTIVE = 'ACTIVE';

    protected static array $PRICES = [
        'prod' => [
            'month' => "price_1PQY8rLLgg6l7qY92ZFwn52T",
            "year" => "price_1PPYVHLLgg6l7qY9b2F2i3Cc",
            'trial' => "+ 15 days"
        ],
        'test' => [
            'month' => "price_1QXjKtLLgg6l7qY9pvXaAWGp",
            "year" => "price_1QXjLMLLgg6l7qY9jVfKkB6i"
        ],
        'dev' => [
            'month' => "price_1QXjKtLLgg6l7qY9pvXaAWGp",
            "year" => "price_1QXjLMLLgg6l7qY9jVfKkB6i",
            'trial' => "+ 15 days"
        ]
    ];

    public function __construct(ApplicationService $applicationService, ActivityService $activityService, Security $security)
    {
        parent::__construct($applicationService, $activityService);
        $this->security = $security;
    }

    static public function getAllowedSubscriptionTypes(Subscription $subscription): array
    {
        $hasWorkflowEnabled = !empty(array_filter($subscription->getOrganism()->getApplications()->toArray(), fn(Application $app) => $app->getAppId() === self::$APP_ID && $app->getEnabled() === true));
        return [
            'training' => $hasWorkflowEnabled ? [$subscription->getTrainingType()] : SubscriptionTrainingTypes::getPaidTrainingTypesForApps(),
            'certifier' => $hasWorkflowEnabled ? [$subscription->getCertifierType()] : ($subscription->isAllowCertifierPlus() ? [$subscription->getCertifierType()] : [])
        ];
    }

    /**
     * @inheritDoc
     */
    public function enabled(Application $application): void
    {
        //real enabled is done in a workflow
        //https://automator.wedof.fr/workflow/Y2U2s0voXrySo6Db
        // TODO: Implement enabled() method.
    }

    /**
     * @inheritDoc
     */
    public function disabled(Application $application): void
    {
        //real disabled is done in a workflow
        //https://automator.wedof.fr/workflow/Y2U2s0voXrySo6Db
    }

    /**
     * @inheritDoc
     */
    public function beforeEnable(Application $application): void
    {
        //saved in service after
        if ($application->getState() == ApplicationStates::DISABLED()) {
            $application->setState($application->getEndDate() == null ? ApplicationStates::PENDING_ENABLE_TRIAL() : ApplicationStates::PENDING_ENABLE());
        }
    }

    /**
     * @inheritDoc
     */
    public function beforeDisable(Application $application): void
    {
        $metadata = $application->getMetadata();
        if ($metadata) {
            // Clear cached credentials
            unset($metadata['token']);
            unset($metadata['projectId']);

            // Clear any old user-specific cached credentials
            $keysToRemove = [];
            foreach ($metadata as $key => $value) {
                if (strpos($key, 'cached_auth_') === 0) {
                    $keysToRemove[] = $key;
                }
            }
            foreach ($keysToRemove as $key) {
                unset($metadata[$key]);
            }
            $application->setMetadata($metadata);
            $this->applicationService->updateMetadata($application, $metadata);
        }
    }

    /**
     * @inheritDoc
     */
    public function onNewOAuth(Application $application): void
    {
        // TODO: Implement onNewOAuth() method.
    }

    /**
     * @inheritDoc
     */
    public function onRefreshOAuth(Application $application): void
    {
        // TODO: Implement onRefreshOAuth() method.
    }

    /**
     * @inheritDoc
     */
    public function onUpdateMetadata(Application $application, array $previousMetadata): void
    {
        // TODO: Implement onUpdateMetadata() method.
    }

    /**
     * @inheritDoc
     */
    static public function getApplicationSubscribedEvents(): array
    {
        return [];
    }

    public function show(Application $application): void
    {
        // TODO: Implement onShow() method.
    }

    /**
     * @param Application $application
     * @return array
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    public function auth(Application $application): array
    {
        /** @var User $user */
        $user = $this->security->getUser();
        $cachedAuth = $this->getCachedCredentials($application, $user->getEmail());
        if ($cachedAuth) {
            try {
                $jwt = JWT::encode($cachedAuth, WorkflowWedofApplication::$APP_ID);
                return ['jwt' => $jwt];
            } catch (Exception $e) {
                $this->clearCachedCredentials($application, $user->getEmail());
            }
        }
        $fullUserAuth = $this->signin($application, $user->getEmail());
        if (empty($fullUserAuth['token'])) {
            $adminAuth = $this->signin($application, Tools::getEnvValue('WORKFLOW_ADMIN_USERNAME'));
            $workflowUsers = $this->getUsers($application, $adminAuth['token']);
            $workflowUserKey = array_search($user->getEmail(), array_column($workflowUsers, 'email'));
            if ($workflowUserKey === false) {
                $this->createUser($application, $user, $adminAuth['projectId'], $adminAuth['token']);
                $fullUserAuth = $this->signin($application, $user->getEmail());
            } else if ($workflowUsers[$workflowUserKey]['status'] === self::INACTIVE) {
                if (!$this->activateUser($application, $adminAuth['token'], $workflowUsers[$workflowUserKey])) {
                    throw new WedofBadRequestHttpException("Enable to activate user");
                } else {
                    $fullUserAuth = $this->signin($application, $user->getEmail());
                }
            }
        }
        if (!empty($fullUserAuth['token'])) {
            $this->cacheCredentials($application, $user->getEmail(), $fullUserAuth);
            // Clear any legacy root-level credentials that might have been set by ProcessusMetierService
            $this->clearLegacyCredentials($application);
        }
        try {
            $jwt = JWT::encode($fullUserAuth, WorkflowWedofApplication::$APP_ID);
            return ['jwt' => $jwt];
        } catch (Exception $e) {
            throw new WedofBadRequestHttpException("Enable to auth user");
        }
    }

    /**
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     */
    private function getUsers(Application $application, string $auth): array
    {
        $client = new CurlHttpClient();
        $response = $client->request('GET', $this->getBaseUrl($application) . "/api/v1/users", [
            'headers' => [
                "Authorization" => 'Bearer ' . $auth,
                'Accept' => 'application/json',
                'Content-Type' => 'application/json'
            ]
        ]);
        return json_decode($response->getContent(), true)['data'];
    }

    /**
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     */
    private function signin(Application $application, string $username): ?array
    {
        $client = new CurlHttpClient();
        $response = $client->request('POST', $this->getBaseUrl($application) . "/api/v1/authentication/sign-in", [
            'headers' => [
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ],
            'body' => json_encode([
                "email" => $username,
                "password" => Tools::getEnvValue('WORKFLOW_PASSWORD')
            ])
        ]);
        return $response->getStatusCode() == 200 ? json_decode($response->getContent(), true) : null;
    }

    /**
     * @param Application $application
     * @return string
     */
    private function getBaseUrl(Application $application): string
    {
        return 'https://' . $application->getOrganism()->getSubDomain() . "." . Tools::getEnvValue('WORKFLOW_DOMAIN_SUFFIX');
    }

    /**
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     * @throws Exception
     */
    private function createUser(Application $application, User $user, string $projectId, string $adminToken): ?array
    {
        $client = new CurlHttpClient();
        if ($adminToken) {
            //create invitation
            $response = $client->request('POST', $this->getBaseUrl($application) . "/api/v1/user-invitations", [
                'headers' => [
                    "Authorization" => 'Bearer ' . $adminToken,
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                ],
                'body' => json_encode([
                    "email" => $user->getEmail(),
                    "type" => "PROJECT",
                    "projectRole" => "EDITOR",
                    "projectId" => $projectId
                ])
            ]);
            //validate invitation
            $invitation = json_decode($response->getContent(), true);
            $jwt = JWT::encode([
                "id" => $invitation['id'],
                "iat" => (new DateTime("now"))->getTimestamp(),
                "exp" => (new DateTime("now"))->modify("+ 1 day")->getTimestamp(),
                "iss" => "activepieces"
            ], Tools::getEnvValue("WORKFLOW_JWT_SECRET"));
            $client->request('POST', $this->getBaseUrl($application) . "/api/v1/user-invitations/accept", [
                'headers' => [
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                ],
                'body' => json_encode(["invitationToken" => $jwt])
            ]);
            if (!$response->getContent()) {
                throw new RuntimeException();
            }
        }
        //create user
        $response = $client->request('POST', $this->getBaseUrl($application) . "/api/v1/authentication/sign-up", [
            'headers' => [
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ],
            'body' => json_encode([
                "email" => $user->getEmail(),
                "firstName" => $user->getFirstName(),
                "lastName" => $user->getLastName(),
                "newsLetter" => false,
                "password" => Tools::generateRandomString(6) . "B$1",
                "trackEvents" => true
            ])
        ]);
        $fullUserAuth = $response->getStatusCode() == 200 ? json_decode($response->getContent(), true) : null;
        //create email validation otp
        $response = $client->request('POST', $this->getBaseUrl($application) . "/api/v1/otp", [
            'headers' => [
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ],
            'body' => json_encode([
                "email" => $user->getEmail(),
                "type" => "EMAIL_VERIFICATION"
            ])
        ]);
        if ($response->getStatusCode() == 204) {
            //validate otp
            $response = $client->request('POST', $this->getBaseUrl($application) . "/api/v1/authn/local/verify-email", [
                'headers' => [
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                ],
                'body' => json_encode([
                    "otp" => Tools::getEnvValue("WORKFLOW_OTP"),
                    "userId" => $fullUserAuth['id']
                ])
            ]);
            if ($response->getStatusCode() == 200) {
                return $fullUserAuth;
            }
        }
        return null;
    }

    /**
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     */
    private function activateUser(Application $application, string $adminToken, array $workflowUser): bool
    {
        $client = new CurlHttpClient();
        $response = $client->request('POST', $this->getBaseUrl($application) . "/api/v1/users/" . $workflowUser['id'], [
            'headers' => [
                "Authorization" => 'Bearer ' . $adminToken,
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ],
            'body' => json_encode([
                "status" => self::ACTIVE
            ])
        ]);
        return json_decode($response->getContent(), true)['status'] === self::ACTIVE;
    }

    /**
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     */
    private function desactivateUser(Application $application, string $adminToken, array $workflowUser): bool
    {
        $client = new CurlHttpClient();
        $response = $client->request('POST', $this->getBaseUrl($application) . "/api/v1/users/" . $workflowUser['id'], [
            'headers' => [
                "Authorization" => 'Bearer ' . $adminToken,
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ],
            'body' => json_encode([
                "status" => self::INACTIVE
            ])
        ]);
        return json_decode($response->getContent(), true)['status'] === self::INACTIVE;
    }

    /**
     *
     * @param Application $application
     * @param string $userEmail
     * @return array|null
     */
    private function getCachedCredentials(Application $application, string $userEmail): ?array
    {
        $metadata = $application->getMetadata() ?? [];
        if (!empty($metadata['token']) && !empty($metadata['projectId'])) {
            return [
                'token' => $metadata['token'],
                'projectId' => $metadata['projectId']
            ];
        }
        return null;
    }

    /**
     *
     * @param Application $application
     * @param string $userEmail
     * @param array $authData
     * @return void
     */
    private function cacheCredentials(Application $application, string $userEmail, array $authData): void
    {
        $metadata = $application->getMetadata() ?? [];

        // Store only the minimum required fields for JWT creation at root level
        $metadata['token'] = $authData['token'];
        $metadata['projectId'] = $authData['projectId'];

        $application->setMetadata($metadata);
        $this->applicationService->updateMetadata($application, $metadata);
    }

    /**
     *
     * @param Application $application
     * @param string $userEmail
     * @return void
     */
    private function clearCachedCredentials(Application $application, string $userEmail): void
    {
        $metadata = $application->getMetadata() ?? [];

        if (isset($metadata['token']) || isset($metadata['projectId'])) {
            unset($metadata['token']);
            unset($metadata['projectId']);
            $application->setMetadata($metadata);
            $this->applicationService->updateMetadata($application, $metadata);
        }
    }

    /**
     * Clear legacy root-level credentials that might have been set by ProcessusMetierService
     * @param Application $application
     * @return void
     */
    private function clearLegacyCredentials(Application $application): void
    {
        $metadata = $application->getMetadata() ?? [];
        $hasLegacyCredentials = isset($metadata['projectId']) || isset($metadata['token']);

        if ($hasLegacyCredentials) {
            unset($metadata['projectId']);
            unset($metadata['token']);
            $application->setMetadata($metadata);
            $this->applicationService->updateMetadata($application, $metadata);
        }
    }
}
