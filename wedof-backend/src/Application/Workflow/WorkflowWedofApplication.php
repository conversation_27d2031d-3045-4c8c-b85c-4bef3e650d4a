<?php
// src/Application/Workflow/WorkflowWedofApplication.php
namespace App\Application\Workflow;

use App\Application\WedofApplication;
use App\Entity\Application;
use App\Entity\Subscription;
use App\Entity\User;
use App\Exception\WedofBadRequestHttpException;
use App\Library\utils\enums\ApplicationStates;
use App\Library\utils\enums\SubscriptionTrainingTypes;
use App\Library\utils\Tools;
use App\Service\ActivityService;
use App\Service\ApplicationService;
use DateTime;
use Exception;
use Firebase\JWT\JWT;
use RuntimeException;
use Symfony\Component\HttpClient\CurlHttpClient;
use Symfony\Component\Security\Core\Security;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;

class WorkflowWedofApplication extends WedofApplication
{
    protected static string $APP_ID = "workflow";
    protected static array $METHODS = ["auth"];
    private Security $security;
    private const INACTIVE = 'INACTIVE';
    private const ACTIVE = 'ACTIVE';

    protected static array $PRICES = [
        'prod' => [
            'month' => "price_1PQY8rLLgg6l7qY92ZFwn52T",
            "year" => "price_1PPYVHLLgg6l7qY9b2F2i3Cc",
            'trial' => "+ 15 days"
        ],
        'test' => [
            'month' => "price_1QXjKtLLgg6l7qY9pvXaAWGp",
            "year" => "price_1QXjLMLLgg6l7qY9jVfKkB6i"
        ],
        'dev' => [
            'month' => "price_1QXjKtLLgg6l7qY9pvXaAWGp",
            "year" => "price_1QXjLMLLgg6l7qY9jVfKkB6i",
            'trial' => "+ 15 days"
        ]
    ];

    public function __construct(ApplicationService $applicationService, ActivityService $activityService, Security $security)
    {
        parent::__construct($applicationService, $activityService);
        $this->security = $security;
    }

    static public function getAllowedSubscriptionTypes(Subscription $subscription): array
    {
        $hasWorkflowEnabled = !empty(array_filter($subscription->getOrganism()->getApplications()->toArray(), fn(Application $app) => $app->getAppId() === self::$APP_ID && $app->getEnabled() === true));
        return [
            'training' => $hasWorkflowEnabled ? [$subscription->getTrainingType()] : SubscriptionTrainingTypes::getPaidTrainingTypesForApps(),
            'certifier' => $hasWorkflowEnabled ? [$subscription->getCertifierType()] : ($subscription->isAllowCertifierPlus() ? [$subscription->getCertifierType()] : [])
        ];
    }

    /**
     * @inheritDoc
     */
    public function enabled(Application $application): void
    {
        //real enabled is done in a workflow
        //https://automator.wedof.fr/workflow/Y2U2s0voXrySo6Db
        // TODO: Implement enabled() method.
    }

    /**
     * @inheritDoc
     */
    public function disabled(Application $application): void
    {
        //real disabled is done in a workflow
        //https://automator.wedof.fr/workflow/Y2U2s0voXrySo6Db
    }

    /**
     * @inheritDoc
     */
    public function beforeEnable(Application $application): void
    {
        //saved in service after
        if ($application->getState() == ApplicationStates::DISABLED()) {
            $application->setState($application->getEndDate() == null ? ApplicationStates::PENDING_ENABLE_TRIAL() : ApplicationStates::PENDING_ENABLE());
        }
    }

    /**
     * @inheritDoc
     */
    public function beforeDisable(Application $application): void
    {
        $metadata = $application->getMetadata();
        if ($metadata) {
            unset($metadata['token']);
            unset($metadata['projectId']);
            unset($metadata['platformId']);
            unset($metadata['id']);
            unset($metadata['firstName']);
            unset($metadata['lastName']);
            $keysToRemove = [];
            foreach ($metadata as $key => $value) {
                if (strpos($key, 'cached_auth_') === 0) {
                    $keysToRemove[] = $key;
                }
            }
            foreach ($keysToRemove as $key) {
                unset($metadata[$key]);
            }
            $application->setMetadata($metadata);
            $this->applicationService->updateMetadata($application, $metadata);
        }
    }

    /**
     * @inheritDoc
     */
    public function onNewOAuth(Application $application): void
    {
        // TODO: Implement onNewOAuth() method.
    }

    /**
     * @inheritDoc
     */
    public function onRefreshOAuth(Application $application): void
    {
        // TODO: Implement onRefreshOAuth() method.
    }

    /**
     * @inheritDoc
     */
    public function onUpdateMetadata(Application $application, array $previousMetadata): void
    {
        // TODO: Implement onUpdateMetadata() method.
    }

    /**
     * @inheritDoc
     */
    static public function getApplicationSubscribedEvents(): array
    {
        return [];
    }

    public function show(Application $application): void
    {
        // TODO: Implement onShow() method.
    }

    /**
     * @param Application $application
     * @return array
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    public function auth(Application $application): array
    {
        /** @var User $user */
        $user = $this->security->getUser();
        $cachedAuth = $this->getCachedCredentials($application, $user->getEmail());
        if ($cachedAuth) {
            try {
                // Ensure cached data is complete
                $completeCachedAuth = $this->ensureCompleteUserData($cachedAuth, $user);
                // Debug: Log cached credentials
                error_log("WorkflowWedofApplication - Using cached auth: " . json_encode($completeCachedAuth));
                $jwt = JWT::encode($completeCachedAuth, WorkflowWedofApplication::$APP_ID);
                error_log("WorkflowWedofApplication - Cached JWT: " . $jwt);
                return ['jwt' => $jwt];
            } catch (Exception $e) {
                error_log("WorkflowWedofApplication - Cached JWT encoding error: " . $e->getMessage());
                $this->clearCachedCredentials($application, $user->getEmail());
            }
        }
        $fullUserAuth = $this->signin($application, $user->getEmail());
        // Debug: Log the actual authentication response structure
        error_log("WorkflowWedofApplication - Full auth response: " . json_encode($fullUserAuth));
        if (empty($fullUserAuth['token'])) {
            $adminAuth = $this->signin($application, Tools::getEnvValue('WORKFLOW_ADMIN_USERNAME'));
            $workflowUsers = $this->getUsers($application, $adminAuth['token']);
            $workflowUserKey = array_search($user->getEmail(), array_column($workflowUsers, 'email'));
            if ($workflowUserKey === false) {
                $this->createUser($application, $user, $adminAuth['projectId'], $adminAuth['token']);
                $fullUserAuth = $this->signin($application, $user->getEmail());
            } else if ($workflowUsers[$workflowUserKey]['status'] === self::INACTIVE) {
                if (!$this->activateUser($application, $adminAuth['token'], $workflowUsers[$workflowUserKey])) {
                    throw new WedofBadRequestHttpException("Enable to activate user");
                } else {
                    $fullUserAuth = $this->signin($application, $user->getEmail());
                }
            }
        }
        if (!empty($fullUserAuth['token'])) {
            // Ensure we have complete user data for caching and JWT
            $completeUserAuth = $this->ensureCompleteUserData($fullUserAuth, $user);
            $this->cacheCredentials($application, $user->getEmail(), $completeUserAuth);

            try {
                // Debug: Log what we're encoding in JWT
                error_log("WorkflowWedofApplication - JWT payload: " . json_encode($completeUserAuth));
                $jwt = JWT::encode($completeUserAuth, WorkflowWedofApplication::$APP_ID);
                error_log("WorkflowWedofApplication - Generated JWT: " . $jwt);
                return ['jwt' => $jwt];
            } catch (Exception $e) {
                error_log("WorkflowWedofApplication - JWT encoding error: " . $e->getMessage());
                throw new WedofBadRequestHttpException("Enable to auth user");
            }
        } else {
            error_log("WorkflowWedofApplication - No token in auth response");
            throw new WedofBadRequestHttpException("Authentication failed - no token received");
        }
    }

    /**
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     */
    private function getUsers(Application $application, string $auth): array
    {
        $client = new CurlHttpClient();
        $response = $client->request('GET', $this->getBaseUrl($application) . "/api/v1/users", [
            'headers' => [
                "Authorization" => 'Bearer ' . $auth,
                'Accept' => 'application/json',
                'Content-Type' => 'application/json'
            ]
        ]);
        return json_decode($response->getContent(), true)['data'];
    }

    /**
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     */
    private function signin(Application $application, string $username): ?array
    {
        $client = new CurlHttpClient();
        $response = $client->request('POST', $this->getBaseUrl($application) . "/api/v1/authentication/sign-in", [
            'headers' => [
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ],
            'body' => json_encode([
                "email" => $username,
                "password" => Tools::getEnvValue('WORKFLOW_PASSWORD')
            ])
        ]);
        if ($response->getStatusCode() == 200) {
            $responseData = json_decode($response->getContent(), true);
            error_log("WorkflowWedofApplication - Signin API response: " . json_encode($responseData));
            return $responseData;
        } else {
            error_log("WorkflowWedofApplication - Signin API failed with status: " . $response->getStatusCode());
            error_log("WorkflowWedofApplication - Signin API error response: " . $response->getContent());
            return null;
        }
    }

    /**
     * @param Application $application
     * @return string
     */
    private function getBaseUrl(Application $application): string
    {
        return 'https://' . $application->getOrganism()->getSubDomain() . "." . Tools::getEnvValue('WORKFLOW_DOMAIN_SUFFIX');
    }

    /**
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     * @throws Exception
     */
    private function createUser(Application $application, User $user, string $projectId, string $adminToken): ?array
    {
        $client = new CurlHttpClient();
        if ($adminToken) {
            $response = $client->request('POST', $this->getBaseUrl($application) . "/api/v1/user-invitations", [
                'headers' => [
                    "Authorization" => 'Bearer ' . $adminToken,
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                ],
                'body' => json_encode([
                    "email" => $user->getEmail(),
                    "type" => "PROJECT",
                    "projectRole" => "EDITOR",
                    "projectId" => $projectId
                ])
            ]);
            $invitation = json_decode($response->getContent(), true);
            $jwt = JWT::encode([
                "id" => $invitation['id'],
                "iat" => (new DateTime("now"))->getTimestamp(),
                "exp" => (new DateTime("now"))->modify("+ 1 day")->getTimestamp(),
                "iss" => "activepieces"
            ], Tools::getEnvValue("WORKFLOW_JWT_SECRET"));
            $client->request('POST', $this->getBaseUrl($application) . "/api/v1/user-invitations/accept", [
                'headers' => [
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                ],
                'body' => json_encode(["invitationToken" => $jwt])
            ]);
            if (!$response->getContent()) {
                throw new RuntimeException();
            }
        }
        $response = $client->request('POST', $this->getBaseUrl($application) . "/api/v1/authentication/sign-up", [
            'headers' => [
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ],
            'body' => json_encode([
                "email" => $user->getEmail(),
                "firstName" => $user->getFirstName(),
                "lastName" => $user->getLastName(),
                "newsLetter" => false,
                "password" => Tools::generateRandomString(6) . "B$1",
                "trackEvents" => true
            ])
        ]);
        $fullUserAuth = $response->getStatusCode() == 200 ? json_decode($response->getContent(), true) : null;
        $response = $client->request('POST', $this->getBaseUrl($application) . "/api/v1/otp", [
            'headers' => [
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ],
            'body' => json_encode([
                "email" => $user->getEmail(),
                "type" => "EMAIL_VERIFICATION"
            ])
        ]);
        if ($response->getStatusCode() == 204) {
            $response = $client->request('POST', $this->getBaseUrl($application) . "/api/v1/authn/local/verify-email", [
                'headers' => [
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                ],
                'body' => json_encode([
                    "otp" => Tools::getEnvValue("WORKFLOW_OTP"),
                    "userId" => $fullUserAuth['id']
                ])
            ]);
            if ($response->getStatusCode() == 200) {
                return $fullUserAuth;
            }
        }
        return null;
    }

    /**
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     */
    private function activateUser(Application $application, string $adminToken, array $workflowUser): bool
    {
        $client = new CurlHttpClient();
        $response = $client->request('POST', $this->getBaseUrl($application) . "/api/v1/users/" . $workflowUser['id'], [
            'headers' => [
                "Authorization" => 'Bearer ' . $adminToken,
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ],
            'body' => json_encode([
                "status" => self::ACTIVE
            ])
        ]);
        return json_decode($response->getContent(), true)['status'] === self::ACTIVE;
    }

    /**
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     */
    private function desactivateUser(Application $application, string $adminToken, array $workflowUser): bool
    {
        $client = new CurlHttpClient();
        $response = $client->request('POST', $this->getBaseUrl($application) . "/api/v1/users/" . $workflowUser['id'], [
            'headers' => [
                "Authorization" => 'Bearer ' . $adminToken,
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ],
            'body' => json_encode([
                "status" => self::INACTIVE
            ])
        ]);
        return json_decode($response->getContent(), true)['status'] === self::INACTIVE;
    }

    /**
     *
     * @param Application $application
     * @param string $userEmail
     * @return array|null
     */
    private function getCachedCredentials(Application $application, string $userEmail): ?array
    {
        $metadata = $application->getMetadata() ?? [];
        error_log("WorkflowWedofApplication - Current metadata: " . json_encode($metadata));

        if (!empty($metadata['token']) && !empty($metadata['projectId']) && !empty($metadata['platformId'])) {
            $cachedData = [
                'token' => $metadata['token'],
                'projectId' => $metadata['projectId'],
                'platformId' => $metadata['platformId']
            ];
            if (!empty($metadata['id'])) {
                $cachedData['id'] = $metadata['id'];
            }
            if (!empty($metadata['firstName'])) {
                $cachedData['firstName'] = $metadata['firstName'];
            }
            if (!empty($metadata['lastName'])) {
                $cachedData['lastName'] = $metadata['lastName'];
            }
            error_log("WorkflowWedofApplication - Retrieved cached data: " . json_encode($cachedData));
            return $cachedData;
        }

        error_log("WorkflowWedofApplication - No valid cached credentials found");
        return null;
    }

    /**
     *
     * @param Application $application
     * @param string $userEmail
     * @param array $authData
     * @return void
     */
    private function cacheCredentials(Application $application, string $userEmail, array $authData): void
    {
        $metadata = $application->getMetadata() ?? [];
        if (empty($authData['token']) || empty($authData['projectId']) || empty($authData['platformId'])) {
            error_log("WorkflowWedofApplication - Cannot cache: missing required fields in authData: " . json_encode($authData));
            return;
        }
        $metadata['token'] = $authData['token'];
        $metadata['projectId'] = $authData['projectId'];
        $metadata['platformId'] = $authData['platformId'];
        if (!empty($authData['id'])) {
            $metadata['id'] = $authData['id'];
        }
        if (!empty($authData['firstName'])) {
            $metadata['firstName'] = $authData['firstName'];
        }
        if (!empty($authData['lastName'])) {
            $metadata['lastName'] = $authData['lastName'];
        }

        error_log("WorkflowWedofApplication - Caching metadata: " . json_encode($metadata));
        $application->setMetadata($metadata);
        $this->applicationService->updateMetadata($application, $metadata);
    }

    /**
     *
     * @param Application $application
     * @param string $userEmail
     * @return void
     */
    private function clearCachedCredentials(Application $application, string $userEmail): void
    {
        $metadata = $application->getMetadata() ?? [];
        if (isset($metadata['token']) || isset($metadata['projectId']) || isset($metadata['platformId']) ||
            isset($metadata['id']) || isset($metadata['firstName']) || isset($metadata['lastName'])) {
            unset($metadata['token']);
            unset($metadata['projectId']);
            unset($metadata['platformId']);
            unset($metadata['id']);
            unset($metadata['firstName']);
            unset($metadata['lastName']);
            $application->setMetadata($metadata);
            $this->applicationService->updateMetadata($application, $metadata);
        }
    }

    /**
     * Ensure the authentication data contains all required fields for frontend compatibility
     *
     * @param array $authData
     * @param User $user
     * @return array
     */
    private function ensureCompleteUserData(array $authData, User $user): array
    {
        // Start with the original auth data
        $completeData = $authData;

        // Ensure essential fields are present
        if (empty($completeData['firstName']) && $user->getFirstName()) {
            $completeData['firstName'] = $user->getFirstName();
            error_log("WorkflowWedofApplication - Added missing firstName from User entity");
        }

        if (empty($completeData['lastName']) && $user->getLastName()) {
            $completeData['lastName'] = $user->getLastName();
            error_log("WorkflowWedofApplication - Added missing lastName from User entity");
        }

        // Ensure we have an email field
        if (empty($completeData['email'])) {
            $completeData['email'] = $user->getEmail();
        }

        // Log the final complete data structure
        error_log("WorkflowWedofApplication - Complete user data: " . json_encode($completeData));

        return $completeData;
    }


}
