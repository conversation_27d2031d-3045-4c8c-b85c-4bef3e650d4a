<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="a85b9800-4d3f-4d18-b786-85b05f1fdfb2" name="Changes" comment="fix(T8516): desoleee">
      <change beforePath="$PROJECT_DIR$/wedof-backend/.env.dev" beforeDir="false" afterPath="$PROJECT_DIR$/wedof-backend/.env.dev" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/wedof-backend/src/Application/Workflow/WorkflowWedofApplication.php" beforeDir="false" afterPath="$PROJECT_DIR$/wedof-backend/src/Application/Workflow/WorkflowWedofApplication.php" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/wedof-backend/src/Service/CertificationPartnerAuditService.php" beforeDir="false" afterPath="$PROJECT_DIR$/wedof-backend/src/Service/CertificationPartnerAuditService.php" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/wedof-frontend/src/app/shared/certification/certification-audit-template/certification-audit-criteria-form/certification-audit-criteria-form.component.scss" beforeDir="false" afterPath="$PROJECT_DIR$/wedof-frontend/src/app/shared/certification/certification-audit-template/certification-audit-criteria-form/certification-audit-criteria-form.component.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/wedof-frontend/src/app/shared/certification/certification-audit-template/certification-audit-criteria-form/certification-audit-criteria-form.component.ts" beforeDir="false" afterPath="$PROJECT_DIR$/wedof-frontend/src/app/shared/certification/certification-audit-template/certification-audit-criteria-form/certification-audit-criteria-form.component.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/wedof-frontend/src/app/shared/sides/workflow-runs-card/workflow-runs-card.component.ts" beforeDir="false" afterPath="$PROJECT_DIR$/wedof-frontend/src/app/shared/sides/workflow-runs-card/workflow-runs-card.component.ts" afterDir="false" />
    </list>
    <list id="ec18849f-ba4e-4b46-8487-9370d9b90a39" name="sites AI organismes" comment="sites AI organismes">
      <change beforePath="$PROJECT_DIR$/wedof-backend/src/Library/utils/Tools.php" beforeDir="false" afterPath="$PROJECT_DIR$/wedof-backend/src/Library/utils/Tools.php" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/wedof-backend/src/Service/DataProviders/AutomatorApiService.php" beforeDir="false" afterPath="$PROJECT_DIR$/wedof-backend/src/Service/DataProviders/AutomatorApiService.php" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ComposerSettings" synchronizationState="SYNCHRONIZE">
    <pharConfigPath>$PROJECT_DIR$/wedof-backend/composer.json</pharConfigPath>
    <execution>
      <phar pharPath="$PROJECT_DIR$/composer.phar" />
    </execution>
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="HTML File" />
        <option value="TypeScript File" />
        <option value="JavaScript File" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="PUSH_AUTO_UPDATE" value="true" />
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/wedof-backend" />
    <option name="ROOT_SYNC" value="SYNC" />
    <option name="UPDATE_TYPE" value="REBASE" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {}
}</component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;**************:w-edof/wedof-backend.git&quot;,
    &quot;accountId&quot;: &quot;acb977c5-cf6d-4a97-9391-1083f0fb8934&quot;
  }
}</component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="McpProjectServerCommands">
    <McpServerCommand>
      <option name="enabled" value="true" />
      <option name="name" value="nx-mcp" />
      <option name="programPath" value="npx" />
      <option name="arguments" value="-y nx-mcp@latest /Users/<USER>" />
      <option name="workingDirectory" value="" />
      <envs />
    </McpServerCommand>
  </component>
  <component name="PhpDebugGeneral" listening_started="true" notify_if_session_was_finished_without_being_paused="false" show_user_defined_constants="false" />
  <component name="PhpServers">
    <servers>
      <server host="kagilum.localhost" id="f2704d41-9e42-41aa-ad1f-8a56577bcf0a" name="kagilum.localhost" port="8000" />
    </servers>
  </component>
  <component name="PhpWebServerValidation" path_to_validation_script="$PROJECT_DIR$" selected_validation_type="LOCAL" web_path_to_validation_script="http://127.0.0.1/" />
  <component name="PhpWorkspaceProjectConfiguration" interpreter_name="PHP">
    <include_path>
      <path value="$PROJECT_DIR$/wedof-backend/vendor/composer" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/jms/serializer-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/jms/metadata" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/jms/serializer" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/psr/container" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/psr/cache" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/psr/simple-cache" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/psr/http-factory" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/psr/http-client" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/psr/log" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/vich/uploader-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/voku/simple_html_dom" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/sllh/iso-codes-validator" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/twig/twig" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/embed/embed" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/lexik/jwt-authentication-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/voku/html-min" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/brick/math" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/beelab/tag-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/defuse/php-encryption" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/meyfa/php-svg" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/nikic/php-parser" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/gpslab/geoip2" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/league/oauth2-server-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/ezyang/htmlpurifier" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/geoip2/geoip2" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/league/uri-interfaces" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/league/uri" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/league/oauth2-google" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/league/oauth2-server" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/matomo/device-detector" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/namshi/jose" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/league/event" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/league/oauth2-client" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/ramsey/collection" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/ramsey/uuid" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/nelmio/api-doc-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/nyholm/psr7" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/yectep/phpspreadsheet-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/egulias/email-validator" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/sensio/framework-extra-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/stripe/stripe-php" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/knplabs/knp-components" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/knplabs/knp-paginator-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/giggsey/locale" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/giggsey/libphonenumber-for-php" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/monolog/monolog" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/myclabs/php-enum" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/laminas/laminas-code" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/maxmind/web-service-common" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/phar-io/version" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/myclabs/deep-copy" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/phar-io/manifest" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/phpunit/phpunit" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/phpunit/php-text-template" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/phpunit/php-timer" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/phpunit/php-token-stream" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/scienta/doctrine-json-functions" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/framework-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/phpunit/php-code-coverage" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/phpunit/php-file-iterator" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/polyfill-intl-icu" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/form" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/monolog-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/slack-notifier" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/polyfill-php73" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/serializer" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/amqp-messenger" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/amazon-mailer" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/validator" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/monolog-bridge" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/polyfill-php80" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/dotenv" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/polyfill-php81" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/polyfill-php72" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/cache" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/asset" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/translation" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/translation-contracts" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/browser-kit" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/security-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/security-http" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/expression-language" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/notifier" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/config" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/lock" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/var-dumper" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/event-dispatcher" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/phpunit-bridge" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/options-resolver" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/security-csrf" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/mailer" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/filesystem" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/maker-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/twig-bridge" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/redis-messenger" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/polyfill-intl-idn" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/polyfill-intl-normalizer" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/css-selector" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/security-guard" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/dom-crawler" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/property-access" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/event-dispatcher-contracts" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/messenger" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/http-client" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/stopwatch" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/twig-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/finder" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/http-kernel" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/yaml" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/doctrine-messenger" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/psr-http-message-bridge" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/security-core" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/mime" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/rate-limiter" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/service-contracts" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/http-client-contracts" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/deprecation-contracts" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/routing" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/doctrine-bridge" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/var-exporter" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/property-info" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/http-foundation" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/cache-contracts" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/dependency-injection" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/flex" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/error-handler" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/console" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/password-hasher" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/process" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/zircote/swagger-php" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/2captcha/2captcha" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/theseer/tokenizer" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/beberlei/doctrineextensions" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/doctrine/doctrine-migrations-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/doctrine/cache" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/doctrine/lexer" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/doctrine/migrations" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/doctrine/data-fixtures" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/doctrine/annotations" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/doctrine/common" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/doctrine/dbal" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/doctrine/inflector" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/doctrine/instantiator" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/doctrine/orm" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/doctrine/persistence" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/doctrine/doctrine-fixtures-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/doctrine/sql-formatter" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/doctrine/event-manager" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/doctrine/collections" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/firebase/php-jwt" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/gesdinet/jwt-refresh-token-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/doctrine/doctrine-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/fakerphp/faker" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/overtrue/pinyin" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/async-aws/core" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/lcobucci/jwt" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/lcobucci/clock" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/lasserafn/php-string-script-language" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/lasserafn/php-initials" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/async-aws/ses" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/lasserafn/php-initial-avatar-generator" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/markbaker/matrix" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/phpoffice/phpspreadsheet" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/maennchen/zipstream-php" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/markbaker/complex" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/sebastian/global-state" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/sebastian/object-enumerator" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/ralouphie/getallheaders" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/sebastian/comparator" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/sebastian/diff" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/sebastian/recursion-context" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/sebastian/object-reflector" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/sebastian/code-unit-reverse-lookup" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/sebastian/environment" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/sebastian/exporter" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/sebastian/type" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/sebastian/version" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/zenstruck/foundry" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/zenstruck/assert" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/sebastian/resource-operations" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/guzzlehttp/psr7" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/guzzlehttp/guzzle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/zenstruck/callback" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/guzzlehttp/promises" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/willdurand/negotiation" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/willdurand/hateoas-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/maxmind-db/reader" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/willdurand/jsonp-callback-validator" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/intervention/image" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfonycasts/verify-email-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/willdurand/hateoas" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/friendsofphp/proxy-manager-lts" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/mustangostang/spyc" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/adam-paterson/oauth2-slack" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/knpuniversity/oauth2-client-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/ronanguilloux/isocodes" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/simplehtmldom/simplehtmldom" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/stevenmaguire/oauth2-salesforce" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/friendsofsymfony/rest-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/proxy-manager-bridge" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/nelmio/cors-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/minishlink/web-push" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/spomky-labs/base64url" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/bentools/webpush-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/chillerlan/php-qrcode" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/chillerlan/php-settings-container" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/web-token/jwt-signature-algorithm-ecdsa" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/web-token/jwt-key-mgmt" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/web-token/jwt-util-ecc" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/web-token/jwt-core" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/web-token/jwt-signature" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/fgrosse/phpasn1" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/ml/iri" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/ml/json-ld" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/aws/aws-crt-php" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/aws/aws-sdk-php" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/oscarotero/html-parser" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/mtdowling/jmespath.php" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/google-mailer" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/knplabs/gaufrette" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/knplabs/knp-gaufrette-bundle" />
    </include_path>
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="ProjectErrors" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2VLW3niHPiF83iJd7z8eOtWGjhs" />
  <component name="ProjectLevelVcsManager">
    <OptionsSetting value="false" id="Update" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "DefaultHtmlFileTemplate": "HTML File",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "Node.js.compteur.js.executor": "Run",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "Shell Script.Back start.executor": "Run",
    "WebServerToolWindowFactoryState": "false",
    "com.codeium.enabled": "true",
    "com.google.cloudcode.ide_session_index": "20231215_0001",
    "dart.analysis.tool.window.visible": "false",
    "git-widget-placeholder": "master",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "/Users/<USER>",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_interpreter_path": "node",
    "nodejs_package_manager_path": "npm",
    "npm.Front start.executor": "Run",
    "php.override.implement.member.chooser.php.doc": "NONE",
    "project.structure.last.edited": "Project",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.2",
    "settings.editor.selected.configurable": "android.project.structure",
    "settings.editor.splitter.proportion": "0.3058104",
    "show.migrate.to.gradle.popup": "false",
    "ts.external.directory.path": "/Users/<USER>/wedof-frontend/node_modules/typescript/lib",
    "vcs.patch.path": "/Users/<USER>/Desktop",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "mysql"
    ],
    "com.intellij.ide.scratch.ScratchImplUtil$2/New Scratch File": [
      "Markdown",
      "JSON"
    ]
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/wedof-frontend/src/assets/images/help-center/guides/certificateurs" />
      <recent name="$PROJECT_DIR$/wedof-backend/src/Event" />
      <recent name="$PROJECT_DIR$/wedof-frontend/src/app/auth/init" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/wedof-frontend/src/app/shared/sides" />
      <recent name="$PROJECT_DIR$/wedof-frontend/src/assets/images/help-center/guides/certificateurs" />
      <recent name="$PROJECT_DIR$/wedof-frontend/src/app/simulator" />
      <recent name="$PROJECT_DIR$/wedof-frontend/src/app/" />
      <recent name="$PROJECT_DIR$/wedof-backend/src/Controller/simulator" />
    </key>
  </component>
  <component name="RunManager" selected="npm.Front start">
    <configuration name="Main" type="PHPUnitRunConfigurationType" factoryName="PHPUnit">
      <TestRunner configuration_file="$PROJECT_DIR$/wedof-backend/phpunit.xml.dist" scope="XML" use_alternative_configuration_file="true" />
      <method v="2" />
    </configuration>
    <configuration name="Back start" type="ShConfigurationType" focusToolWindowBeforeRun="true">
      <option name="SCRIPT_TEXT" value="symfony server:start" />
      <option name="INDEPENDENT_SCRIPT_PATH" value="true" />
      <option name="SCRIPT_PATH" value="" />
      <option name="SCRIPT_OPTIONS" value="" />
      <option name="INDEPENDENT_SCRIPT_WORKING_DIRECTORY" value="true" />
      <option name="SCRIPT_WORKING_DIRECTORY" value="$PROJECT_DIR$/wedof-backend" />
      <option name="INDEPENDENT_INTERPRETER_PATH" value="true" />
      <option name="INTERPRETER_PATH" value="/bin/zsh" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="EXECUTE_IN_TERMINAL" value="true" />
      <option name="EXECUTE_SCRIPT_FILE" value="false" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="Front start" type="js.build_tools.npm">
      <package-json value="$PROJECT_DIR$/wedof-frontend/package.json" />
      <command value="run" />
      <scripts>
        <script value="start:subdomain" />
      </scripts>
      <node-interpreter value="node" />
      <package-manager value="npm" />
      <envs />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="npm.Front start" />
      <item itemvalue="PHPUnit.Main" />
      <item itemvalue="Shell Script.Back start" />
    </list>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="ShelveChangesManager">
    <option name="remove_strategy" value="true" />
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="a85b9800-4d3f-4d18-b786-85b05f1fdfb2" name="Changes" comment="" />
      <changelist id="ec18849f-ba4e-4b46-8487-9370d9b90a39" name="sites AI organismes" comment="" />
      <created>1694615644879</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1694615644879</updated>
      <workItem from="1694615645871" duration="635000" />
      <workItem from="1694619402335" duration="7712000" />
      <workItem from="1694676079321" duration="4002000" />
      <workItem from="1694712449512" duration="76000" />
      <workItem from="1694764185868" duration="36173000" />
      <workItem from="1695108262112" duration="9314000" />
      <workItem from="1695202577101" duration="41000" />
      <workItem from="1695202625558" duration="30000" />
      <workItem from="1695202662934" duration="71483000" />
      <workItem from="1695885933893" duration="723000" />
      <workItem from="1695886666588" duration="1300000" />
      <workItem from="1695891650064" duration="78000" />
      <workItem from="1695891736304" duration="3084000" />
      <workItem from="1695973959252" duration="10204000" />
      <workItem from="1695984656739" duration="155000" />
      <workItem from="1696192640121" duration="5452000" />
      <workItem from="1696282526723" duration="5221000" />
      <workItem from="1696364505399" duration="28458000" />
      <workItem from="1697526155752" duration="231000" />
      <workItem from="1697526395490" duration="5879000" />
      <workItem from="1697533055894" duration="33779000" />
      <workItem from="1697641011173" duration="3024000" />
      <workItem from="1697699188617" duration="5610000" />
      <workItem from="1697736653644" duration="22149000" />
      <workItem from="1698045618866" duration="17323000" />
      <workItem from="1698130645040" duration="40602000" />
      <workItem from="1698390115322" duration="17911000" />
      <workItem from="1699862752815" duration="2976000" />
      <workItem from="1699869345910" duration="119000" />
      <workItem from="1699870372421" duration="16036000" />
      <workItem from="1699949855324" duration="9440000" />
      <workItem from="1700035065572" duration="7045000" />
      <workItem from="1700121905663" duration="11547000" />
      <workItem from="1700159170947" duration="216000" />
      <workItem from="1700210348997" duration="67500000" />
      <workItem from="1700491083176" duration="428000" />
      <workItem from="1700492960251" duration="287000" />
      <workItem from="1700493262477" duration="431000" />
      <workItem from="1700493702951" duration="45000" />
      <workItem from="1700493757091" duration="1177000" />
      <workItem from="1700494981604" duration="3721000" />
      <workItem from="1700500773585" duration="1824000" />
      <workItem from="1700553670083" duration="2637000" />
      <workItem from="1700556835905" duration="1642000" />
      <workItem from="1700559505612" duration="732000" />
      <workItem from="1700560266284" duration="894000" />
      <workItem from="1700561569260" duration="67000" />
      <workItem from="1700561693722" duration="44000" />
      <workItem from="1700561747219" duration="2501000" />
      <workItem from="1700564540678" duration="2077000" />
      <workItem from="1700571714308" duration="8521000" />
      <workItem from="1700581341579" duration="54063000" />
      <workItem from="1700811751421" duration="18369000" />
      <workItem from="1701047667050" duration="11934000" />
      <workItem from="1701087089011" duration="419000" />
      <workItem from="1702282724880" duration="5235000" />
      <workItem from="1702367793557" duration="4016000" />
      <workItem from="1702454547863" duration="748000" />
      <workItem from="1702456709860" duration="24928000" />
      <workItem from="1702491255438" duration="484000" />
      <workItem from="1702491752998" duration="495000" />
      <workItem from="1702540472334" duration="139000" />
      <workItem from="1702540620995" duration="9200000" />
      <workItem from="1702638335132" duration="276000" />
      <workItem from="1702638847178" duration="320000" />
      <workItem from="1702639200692" duration="135000" />
      <workItem from="1702639345711" duration="298000" />
      <workItem from="1702639653721" duration="117000" />
      <workItem from="1702639779207" duration="2233000" />
      <workItem from="1702886711794" duration="328000" />
      <workItem from="1702912833741" duration="10464000" />
      <workItem from="1703060931044" duration="13517000" />
      <workItem from="1703232667927" duration="1708000" />
      <workItem from="1703516447516" duration="122000" />
      <workItem from="1703517646047" duration="23000" />
      <workItem from="1703518482290" duration="28000" />
      <workItem from="1703519707009" duration="244000" />
      <workItem from="1703520906615" duration="15000" />
      <workItem from="1703577516191" duration="2480000" />
      <workItem from="1703584691876" duration="799000" />
      <workItem from="1703595283133" duration="604000" />
      <workItem from="1703673443757" duration="615000" />
      <workItem from="1703674119464" duration="397000" />
      <workItem from="1703682042757" duration="1300000" />
      <workItem from="1703683797421" duration="414000" />
      <workItem from="1703694094629" duration="790000" />
      <workItem from="1703752176862" duration="13428000" />
      <workItem from="1704135980545" duration="4804000" />
      <workItem from="1704312981951" duration="154000" />
      <workItem from="1704704999713" duration="5858000" />
      <workItem from="1704787908995" duration="5320000" />
      <workItem from="1704816217046" duration="42608000" />
      <workItem from="1705052310136" duration="1121000" />
      <workItem from="1705064214721" duration="197000" />
      <workItem from="1705064724810" duration="6325000" />
      <workItem from="1705306022034" duration="21491000" />
      <workItem from="1705392099424" duration="22856000" />
      <workItem from="1705479134737" duration="14237000" />
      <workItem from="1705500526336" duration="7516000" />
      <workItem from="1705564634989" duration="13153000" />
      <workItem from="1705587050597" duration="6262000" />
      <workItem from="1705651836874" duration="18485000" />
      <workItem from="1707077397133" duration="4101000" />
      <workItem from="1707083383107" duration="25000" />
      <workItem from="1707083415787" duration="23000" />
      <workItem from="1707120596003" duration="19176000" />
      <workItem from="1707292344249" duration="50641000" />
      <workItem from="1707725038160" duration="1995000" />
      <workItem from="1707727052953" duration="13498000" />
      <workItem from="1707746743109" duration="8621000" />
      <workItem from="1707759035789" duration="189000" />
      <workItem from="1707759377545" duration="61772000" />
      <workItem from="1707983830301" duration="14483000" />
      <workItem from="1708007178445" duration="6491000" />
      <workItem from="1708073930707" duration="12240000" />
      <workItem from="1708377842990" duration="1049000" />
      <workItem from="1709511181294" duration="5427000" />
      <workItem from="1709653475135" duration="2302000" />
      <workItem from="1709713663407" duration="4925000" />
      <workItem from="1709744067216" duration="686000" />
      <workItem from="1709746372031" duration="1603000" />
      <workItem from="1709817178522" duration="3748000" />
      <workItem from="1709885189847" duration="298000" />
      <workItem from="1709886932624" duration="906000" />
      <workItem from="1710143739601" duration="1053000" />
      <workItem from="1710146444383" duration="8442000" />
      <workItem from="1710329564747" duration="41000" />
      <workItem from="1710335272685" duration="1682000" />
      <workItem from="1710406710881" duration="513000" />
      <workItem from="1710420622891" duration="61000" />
      <workItem from="1714342433573" duration="212000" />
      <workItem from="1714374802656" duration="1214000" />
      <workItem from="1714376279538" duration="3059000" />
      <workItem from="1714395266639" duration="1334000" />
      <workItem from="1714463037680" duration="22000" />
      <workItem from="1714636295836" duration="25000" />
      <workItem from="1714656525521" duration="3274000" />
      <workItem from="1714742908387" duration="44000" />
      <workItem from="1714999446351" duration="70000" />
      <workItem from="1714999518821" duration="1142000" />
      <workItem from="1715000683405" duration="2873000" />
      <workItem from="1715004423544" duration="2065000" />
      <workItem from="1715006542060" duration="224000" />
      <workItem from="1715006792392" duration="1198000" />
      <workItem from="1715073830723" duration="77000" />
      <workItem from="1715377494069" duration="4683000" />
      <workItem from="1715384249302" duration="13304000" />
      <workItem from="1715559170402" duration="1757000" />
      <workItem from="1715723327343" duration="11000" />
      <workItem from="1716362497938" duration="603000" />
      <workItem from="1716793198098" duration="10915000" />
      <workItem from="1716812691453" duration="5630000" />
      <workItem from="1716879774286" duration="5246000" />
      <workItem from="1716966187471" duration="626000" />
      <workItem from="1717399267205" duration="17551000" />
      <workItem from="1717425239203" duration="2937000" />
      <workItem from="1717506519457" duration="466000" />
      <workItem from="1717573001804" duration="3014000" />
      <workItem from="1717576038134" duration="60000" />
      <workItem from="1717576103414" duration="6154000" />
      <workItem from="1717598056681" duration="159000" />
      <workItem from="1717755744103" duration="678000" />
      <workItem from="1718139001431" duration="90000" />
      <workItem from="1719166560084" duration="44000" />
      <workItem from="1719212583673" duration="2296000" />
      <workItem from="1719319433531" duration="1733000" />
      <workItem from="1719394215536" duration="489000" />
      <workItem from="1719572539441" duration="21000" />
      <workItem from="1719831057625" duration="9242000" />
      <workItem from="1720103466726" duration="3139000" />
      <workItem from="1720424917596" duration="24487000" />
      <workItem from="1720769179849" duration="2157000" />
      <workItem from="1721027606392" duration="322000" />
      <workItem from="1721027956236" duration="352000" />
      <workItem from="1721116450353" duration="2851000" />
      <workItem from="1721122073854" duration="1274000" />
      <workItem from="1721200272700" duration="4000" />
      <workItem from="1721200473729" duration="334000" />
      <workItem from="1721255982021" duration="623000" />
      <workItem from="1721292154114" duration="2156000" />
      <workItem from="1721660017771" duration="61000" />
      <workItem from="1721721676318" duration="1055000" />
      <workItem from="1721722968983" duration="119000" />
      <workItem from="1721724156666" duration="1234000" />
      <workItem from="1721729665869" duration="6201000" />
      <workItem from="1721827001678" duration="1591000" />
      <workItem from="1721833512141" duration="300000" />
      <workItem from="1721850802766" duration="1579000" />
      <workItem from="1721898318464" duration="716000" />
      <workItem from="1721978556505" duration="1036000" />
      <workItem from="1721981198500" duration="1537000" />
      <workItem from="1722000410570" duration="1384000" />
      <workItem from="1722245546100" duration="907000" />
      <workItem from="1722289333372" duration="1143000" />
      <workItem from="1722341653528" duration="376000" />
      <workItem from="1722504892213" duration="246000" />
      <workItem from="1722505327957" duration="2176000" />
      <workItem from="1722522778410" duration="821000" />
      <workItem from="1722588878262" duration="1088000" />
      <workItem from="1723018448347" duration="609000" />
      <workItem from="1723022895859" duration="1560000" />
      <workItem from="1723025936474" duration="593000" />
      <workItem from="1723101059638" duration="1181000" />
      <workItem from="1723103254425" duration="9881000" />
      <workItem from="1723122097433" duration="1931000" />
      <workItem from="1723191780542" duration="619000" />
      <workItem from="1723476285093" duration="3000" />
      <workItem from="1724069645826" duration="5931000" />
      <workItem from="1724145021395" duration="958000" />
      <workItem from="1724146380220" duration="969000" />
      <workItem from="1724157017754" duration="4998000" />
      <workItem from="1724228638118" duration="685000" />
      <workItem from="1724312225412" duration="1499000" />
      <workItem from="1724313754802" duration="3443000" />
      <workItem from="1724318468018" duration="1301000" />
      <workItem from="1724327345507" duration="2206000" />
      <workItem from="1724399169418" duration="1646000" />
      <workItem from="1724635737404" duration="15342000" />
      <workItem from="1724673703555" duration="11092000" />
      <workItem from="1724710211817" duration="1665000" />
      <workItem from="1724741909185" duration="22260000" />
      <workItem from="1724834121127" duration="56352000" />
      <workItem from="1725002721705" duration="12260000" />
      <workItem from="1725222017599" duration="27885000" />
      <workItem from="1725348400486" duration="7303000" />
      <workItem from="1725369453471" duration="50000" />
      <workItem from="1725369509570" duration="63000" />
      <workItem from="1725372159079" duration="35220000" />
      <workItem from="1725630873312" duration="3214000" />
      <workItem from="1725936557035" duration="49191000" />
      <workItem from="1726243918244" duration="3801000" />
      <workItem from="1727620421788" duration="1369000" />
      <workItem from="1727680909008" duration="6022000" />
      <workItem from="1727770118003" duration="252000" />
      <workItem from="1727863012645" duration="2936000" />
      <workItem from="1727871532038" duration="2721000" />
      <workItem from="1727961128529" duration="2000" />
      <workItem from="1727963601886" duration="407000" />
      <workItem from="1728036410168" duration="612000" />
      <workItem from="1728295328814" duration="3000" />
      <workItem from="1728374518785" duration="3208000" />
      <workItem from="1728551397545" duration="16386000" />
      <workItem from="1728631893588" duration="2902000" />
      <workItem from="1729028055482" duration="1599000" />
      <workItem from="1729504769737" duration="95000" />
      <workItem from="1729510311122" duration="997000" />
      <workItem from="1729975101246" duration="411000" />
      <workItem from="1729975522589" duration="81000" />
      <workItem from="1729975608166" duration="45000" />
      <workItem from="1729975659455" duration="14163000" />
      <workItem from="1730044519283" duration="18189000" />
      <workItem from="1730104462277" duration="4133000" />
      <workItem from="1730119697899" duration="1963000" />
      <workItem from="1730190088444" duration="13998000" />
      <workItem from="1730277316984" duration="15596000" />
      <workItem from="1730301499984" duration="19951000" />
      <workItem from="1730362692760" duration="117000" />
      <workItem from="1730362815168" duration="12073000" />
      <workItem from="1731022454632" duration="601000" />
      <workItem from="1731400217105" duration="250000" />
      <workItem from="1731508753129" duration="24000" />
      <workItem from="1731509342315" duration="29000" />
      <workItem from="1732525368879" duration="56000" />
      <workItem from="1732550297788" duration="1562000" />
      <workItem from="1732610105839" duration="10753000" />
      <workItem from="1732625109322" duration="11939000" />
      <workItem from="1732695668319" duration="10536000" />
      <workItem from="1732784790998" duration="11708000" />
      <workItem from="1732867605212" duration="18886000" />
      <workItem from="1733022781098" duration="3430000" />
      <workItem from="1733076708383" duration="3656000" />
      <workItem from="1733129239947" duration="178000" />
      <workItem from="1733129421772" duration="10000" />
      <workItem from="1733129604340" duration="4448000" />
      <workItem from="1733215432191" duration="3240000" />
      <workItem from="1733219083474" duration="143000" />
      <workItem from="1733219275799" duration="21000" />
      <workItem from="1733219326923" duration="64000" />
      <workItem from="1733219394988" duration="24000" />
      <workItem from="1733219422826" duration="7135000" />
      <workItem from="1733318885745" duration="2591000" />
      <workItem from="1733390043950" duration="4051000" />
      <workItem from="1733477614656" duration="2293000" />
      <workItem from="1733483040598" duration="5972000" />
      <workItem from="1733490847894" duration="7707000" />
      <workItem from="1734943908050" duration="132000" />
      <workItem from="1734960427533" duration="6820000" />
      <workItem from="1735034171018" duration="14931000" />
      <workItem from="1735053008153" duration="4860000" />
      <workItem from="1735168359480" duration="3093000" />
      <workItem from="1735198837839" duration="19317000" />
      <workItem from="1735223204952" duration="5261000" />
      <workItem from="1735253012501" duration="3979000" />
      <workItem from="1735286813811" duration="8004000" />
      <workItem from="1735294978677" duration="266000" />
      <workItem from="1735295251856" duration="932000" />
      <workItem from="1735296194326" duration="16097000" />
      <workItem from="1735315013785" duration="439000" />
      <workItem from="1735516751765" duration="32822000" />
      <workItem from="1735806105847" duration="8062000" />
      <workItem from="1735816377856" duration="14685000" />
      <workItem from="1735856048165" duration="1174000" />
      <workItem from="1735891841382" duration="19966000" />
      <workItem from="1736034913826" duration="13584000" />
      <workItem from="1736213568658" duration="9323000" />
      <workItem from="1737323553484" duration="2773000" />
      <workItem from="1737360892759" duration="2971000" />
      <workItem from="1737448612732" duration="1750000" />
      <workItem from="1737535687852" duration="171000" />
      <workItem from="1737561040840" duration="1797000" />
      <workItem from="1737601168222" duration="20408000" />
      <workItem from="1737706583088" duration="7456000" />
      <workItem from="1737855223305" duration="2373000" />
      <workItem from="1737907217580" duration="2703000" />
      <workItem from="1737966766661" duration="1029000" />
      <workItem from="1739349325926" duration="1196000" />
      <workItem from="1739791339696" duration="26000" />
      <workItem from="1739791397614" duration="1598000" />
      <workItem from="1739961594675" duration="15267000" />
      <workItem from="1740041658935" duration="66000" />
      <workItem from="1740044807402" duration="1484000" />
      <workItem from="1740068351992" duration="95000" />
      <workItem from="1740130297224" duration="635000" />
      <workItem from="1740152452260" duration="10077000" />
      <workItem from="1740280467437" duration="3906000" />
      <workItem from="1740496521889" duration="10912000" />
      <workItem from="1740617636672" duration="1807000" />
      <workItem from="1740668820823" duration="805000" />
      <workItem from="1740738088723" duration="162000" />
      <workItem from="1740751881397" duration="565000" />
      <workItem from="1741094924626" duration="485000" />
      <workItem from="1741097128262" duration="260000" />
      <workItem from="1741169550893" duration="3549000" />
      <workItem from="1741180810816" duration="113000" />
      <workItem from="1741250134977" duration="597000" />
      <workItem from="1741251106293" duration="2563000" />
      <workItem from="1741572944153" duration="1853000" />
      <workItem from="1741873912109" duration="65000" />
      <workItem from="1741873999227" duration="1655000" />
      <workItem from="1741882991910" duration="115000" />
      <workItem from="1741942453993" duration="12000" />
      <workItem from="1742216581127" duration="3614000" />
      <workItem from="1742285975184" duration="23112000" />
      <workItem from="1742402643106" duration="6101000" />
      <workItem from="1742458288639" duration="1114000" />
      <workItem from="1742460285463" duration="251000" />
      <workItem from="1742464765176" duration="1347000" />
      <workItem from="1742469882747" duration="25501000" />
      <workItem from="1742577352011" duration="130000" />
      <workItem from="1743985709384" duration="182000" />
      <workItem from="1743997497017" duration="7691000" />
      <workItem from="1744022466058" duration="56000" />
      <workItem from="1744022687750" duration="8480000" />
      <workItem from="1744098150036" duration="8585000" />
      <workItem from="1744151810785" duration="367000" />
      <workItem from="1744152886396" duration="40000" />
      <workItem from="1744272290818" duration="110000" />
      <workItem from="1744276103327" duration="5035000" />
      <workItem from="1744381779729" duration="316000" />
      <workItem from="1744710177586" duration="3451000" />
      <workItem from="1744791822340" duration="955000" />
      <workItem from="1744793981008" duration="810000" />
      <workItem from="1744848776568" duration="5332000" />
      <workItem from="1744874486634" duration="22682000" />
      <workItem from="1744929448771" duration="498000" />
      <workItem from="1745098806331" duration="2456000" />
      <workItem from="1745194897243" duration="19306000" />
      <workItem from="1745306325301" duration="1300000" />
      <workItem from="1745395042431" duration="25802000" />
      <workItem from="1745578114399" duration="9530000" />
      <workItem from="1745775917736" duration="23601000" />
      <workItem from="1745824842771" duration="12715000" />
      <workItem from="1745917329442" duration="9210000" />
      <workItem from="1746009483019" duration="10137000" />
      <workItem from="1746145934846" duration="34844000" />
      <workItem from="1746430904479" duration="19060000" />
      <workItem from="1746484047360" duration="5607000" />
      <workItem from="1746519485804" duration="19001000" />
      <workItem from="1746605702772" duration="7959000" />
      <workItem from="1746737788229" duration="307000" />
      <workItem from="1746738250496" duration="30062000" />
      <workItem from="1746801366053" duration="14597000" />
      <workItem from="1747053778123" duration="5631000" />
      <workItem from="1747120670641" duration="5554000" />
      <workItem from="1747127594654" duration="2098000" />
      <workItem from="1747129699113" duration="1507000" />
      <workItem from="1747131650249" duration="1127000" />
      <workItem from="1747132792678" duration="424000" />
      <workItem from="1747133227640" duration="918000" />
      <workItem from="1747134161729" duration="27000" />
      <workItem from="1747134568172" duration="305000" />
      <workItem from="1747138084743" duration="530000" />
      <workItem from="1747138641835" duration="361000" />
      <workItem from="1747139022786" duration="8020000" />
      <workItem from="1747147086495" duration="53351000" />
      <workItem from="1747352731609" duration="19907000" />
      <workItem from="1747436929454" duration="88000" />
      <workItem from="1747601955947" duration="28206000" />
      <workItem from="1747664480909" duration="2937000" />
      <workItem from="1747667743768" duration="47000" />
      <workItem from="1747734353333" duration="15892000" />
      <workItem from="1747811321018" duration="3934000" />
      <workItem from="1747819777794" duration="9819000" />
      <workItem from="1747836948805" duration="34000" />
      <workItem from="1747837014981" duration="3336000" />
      <workItem from="1747897924026" duration="30000" />
      <workItem from="1747898390407" duration="8926000" />
      <workItem from="1747992645203" duration="5577000" />
      <workItem from="1748218432277" duration="1051000" />
      <workItem from="1748243304069" duration="27969000" />
      <workItem from="1748417439813" duration="8431000" />
      <workItem from="1748849169354" duration="915000" />
      <workItem from="1748854231480" duration="4506000" />
      <workItem from="1748867613667" duration="8511000" />
      <workItem from="1748894579955" duration="847000" />
      <workItem from="1748935362380" duration="22395000" />
      <workItem from="1749021951824" duration="11549000" />
      <workItem from="1749540234401" duration="221000" />
      <workItem from="1749566994899" duration="982000" />
      <workItem from="1749629948166" duration="3323000" />
      <workItem from="1749716223879" duration="1237000" />
      <workItem from="1749724314886" duration="10112000" />
      <workItem from="1749801762014" duration="3882000" />
      <workItem from="1750017764449" duration="26273000" />
      <workItem from="1750145108802" duration="2167000" />
      <workItem from="1750147339264" duration="16375000" />
      <workItem from="1750196788768" duration="549000" />
      <workItem from="1750197346672" duration="32000" />
      <workItem from="1750197386848" duration="54000" />
      <workItem from="1750197448880" duration="13170000" />
      <workItem from="1750234626060" duration="8330000" />
      <workItem from="1750253999056" duration="21217000" />
      <workItem from="1750318280272" duration="12282000" />
      <workItem from="1750338805361" duration="15000" />
      <workItem from="1750406264022" duration="3784000" />
      <workItem from="1750760028506" duration="1265000" />
      <workItem from="1750836246423" duration="41547000" />
      <workItem from="1751024030142" duration="5734000" />
      <workItem from="1751059059958" duration="107000" />
      <workItem from="1751171007590" duration="894000" />
      <workItem from="1751268959163" duration="6504000" />
      <workItem from="1751354217049" duration="315000" />
      <workItem from="1751359409230" duration="686000" />
      <workItem from="1751374366771" duration="420000" />
      <workItem from="1751378606991" duration="718000" />
      <workItem from="1751514179464" duration="75000" />
      <workItem from="1751514458445" duration="7729000" />
      <workItem from="1751533164777" duration="48000" />
      <workItem from="1751533238524" duration="36000" />
      <workItem from="1751533278070" duration="131000" />
      <workItem from="1751533419316" duration="8000" />
      <workItem from="1751533455895" duration="307000" />
      <workItem from="1751534921790" duration="563000" />
      <workItem from="1751535754633" duration="149000" />
      <workItem from="1751535904777" duration="6802000" />
    </task>
    <task id="LOCAL-00048" summary="feat(certification-partner-table): &quot;Inverser l'ordre des demandes en traitement par stateLastUpdate&quot;">
      <option name="closed" value="true" />
      <created>1732702064829</created>
      <option name="number" value="00048" />
      <option name="presentableId" value="LOCAL-00048" />
      <option name="project" value="LOCAL" />
      <updated>1732702064829</updated>
    </task>
    <task id="LOCAL-00049" summary="feat(filterOnStateDate): &quot;Ajout enrollmentDate au filtre&quot;">
      <option name="closed" value="true" />
      <created>1733221073522</created>
      <option name="number" value="00049" />
      <option name="presentableId" value="LOCAL-00049" />
      <option name="project" value="LOCAL" />
      <updated>1733221073522</updated>
    </task>
    <task id="LOCAL-00050" summary="fix(filterOnStateDate): &quot;Ajout enrollmentDate au filtre&quot;">
      <option name="closed" value="true" />
      <created>1733222367183</created>
      <option name="number" value="00050" />
      <option name="presentableId" value="LOCAL-00050" />
      <option name="project" value="LOCAL" />
      <updated>1733222367183</updated>
    </task>
    <task id="LOCAL-00051" summary="fix(certificationFolders-filter-Period): &quot;Ajout rolling(WeekFuture,MonthFuture,YearFuture)&quot;">
      <option name="closed" value="true" />
      <created>1733496911638</created>
      <option name="number" value="00051" />
      <option name="presentableId" value="LOCAL-00051" />
      <option name="project" value="LOCAL" />
      <updated>1733496911639</updated>
    </task>
    <task id="LOCAL-00052" summary="feat(audit events): &quot;Avoir event sur Audit.Conforme / Non conforme / Partiellement conforme&quot;">
      <option name="closed" value="true" />
      <created>1735200924508</created>
      <option name="number" value="00052" />
      <option name="presentableId" value="LOCAL-00052" />
      <option name="project" value="LOCAL" />
      <updated>1735200924508</updated>
    </task>
    <task id="LOCAL-00053" summary="fix(audit events): &quot;raising events&quot;">
      <option name="closed" value="true" />
      <created>1735208510229</created>
      <option name="number" value="00053" />
      <option name="presentableId" value="LOCAL-00053" />
      <option name="project" value="LOCAL" />
      <updated>1735208510229</updated>
    </task>
    <task id="LOCAL-00054" summary="fix(audit events): &quot;fixing fix&quot;">
      <option name="closed" value="true" />
      <created>1735209568163</created>
      <option name="number" value="00054" />
      <option name="presentableId" value="LOCAL-00054" />
      <option name="project" value="LOCAL" />
      <updated>1735209568163</updated>
    </task>
    <task id="LOCAL-00055" summary="fix(audit events): &quot;last fix&quot;">
      <option name="closed" value="true" />
      <created>1735210264365</created>
      <option name="number" value="00055" />
      <option name="presentableId" value="LOCAL-00055" />
      <option name="project" value="LOCAL" />
      <updated>1735210264365</updated>
    </task>
    <task id="LOCAL-00056" summary="feat(T-7477):&quot;Ajout niveau conformité pour chaque critére&quot;">
      <option name="closed" value="true" />
      <created>1735299461660</created>
      <option name="number" value="00056" />
      <option name="presentableId" value="LOCAL-00056" />
      <option name="project" value="LOCAL" />
      <updated>1735299461660</updated>
    </task>
    <task id="LOCAL-00057" summary="feat(icon-severity):&quot;ajout icon dans le certification audit creteria form&quot;">
      <option name="closed" value="true" />
      <created>1735919596459</created>
      <option name="number" value="00057" />
      <option name="presentableId" value="LOCAL-00057" />
      <option name="project" value="LOCAL" />
      <updated>1735919596460</updated>
    </task>
    <task id="LOCAL-00058" summary="fix(severity non initialized):&quot;initialisé la séverité par defaut a NONE&quot;">
      <option name="closed" value="true" />
      <created>1735919705393</created>
      <option name="number" value="00058" />
      <option name="presentableId" value="LOCAL-00058" />
      <option name="project" value="LOCAL" />
      <updated>1735919705394</updated>
    </task>
    <task id="LOCAL-00059" summary="fix(bug icon creteria):&quot;par default pas d'icon-&gt; severité = NONE&quot;">
      <option name="closed" value="true" />
      <created>1735919779750</created>
      <option name="number" value="00059" />
      <option name="presentableId" value="LOCAL-00059" />
      <option name="project" value="LOCAL" />
      <updated>1735919779750</updated>
    </task>
    <task id="LOCAL-00060" summary="feat(webhooks):&quot;adding activepieces flow link + bloking acces to webhook settings&quot;">
      <option name="closed" value="true" />
      <created>1737735820870</created>
      <option name="number" value="00060" />
      <option name="presentableId" value="LOCAL-00060" />
      <option name="project" value="LOCAL" />
      <updated>1737735820870</updated>
    </task>
    <task id="LOCAL-00061" summary="fix(zapier-invite):&quot;mettre a jour le lien d'invitation de l'application wedof sur zapier&quot;">
      <option name="closed" value="true" />
      <created>1741095072164</created>
      <option name="number" value="00061" />
      <option name="presentableId" value="LOCAL-00061" />
      <option name="project" value="LOCAL" />
      <updated>1741095072165</updated>
    </task>
    <task id="LOCAL-00062" summary="feat(addUrl):&quot;ajouter un url a la liste des site web d'un partenariat&quot;">
      <option name="closed" value="true" />
      <created>1742555477357</created>
      <option name="number" value="00062" />
      <option name="presentableId" value="LOCAL-00062" />
      <option name="project" value="LOCAL" />
      <updated>1742555477357</updated>
    </task>
    <task id="LOCAL-00063" summary="fix(addUrl):&quot;oublie dans constructeur&quot;">
      <option name="closed" value="true" />
      <created>1742563344309</created>
      <option name="number" value="00063" />
      <option name="presentableId" value="LOCAL-00063" />
      <option name="project" value="LOCAL" />
      <updated>1742563344309</updated>
    </task>
    <task id="LOCAL-00064" summary="fix(addUrl):&quot;rajout params, suppression condition&quot;">
      <option name="closed" value="true" />
      <created>1742566338225</created>
      <option name="number" value="00064" />
      <option name="presentableId" value="LOCAL-00064" />
      <option name="project" value="LOCAL" />
      <updated>1742566338225</updated>
    </task>
    <task id="LOCAL-00065" summary="fix(8006):&quot;extraire le nom du webhook de l'html dans le cas d'activepieces&quot;">
      <option name="closed" value="true" />
      <created>1742567020140</created>
      <option name="number" value="00065" />
      <option name="presentableId" value="LOCAL-00065" />
      <option name="project" value="LOCAL" />
      <updated>1742567020140</updated>
    </task>
    <task id="LOCAL-00066" summary="delete(addUrl):&quot;suppression endpoint addUrl&quot;">
      <option name="closed" value="true" />
      <created>1744712252639</created>
      <option name="number" value="00066" />
      <option name="presentableId" value="LOCAL-00066" />
      <option name="project" value="LOCAL" />
      <updated>1744712252640</updated>
    </task>
    <task id="LOCAL-00067" summary="feat(update-url):&quot;add the possibility to update url list from update endpoint&quot;">
      <option name="closed" value="true" />
      <created>1744883753008</created>
      <option name="number" value="00067" />
      <option name="presentableId" value="LOCAL-00067" />
      <option name="project" value="LOCAL" />
      <updated>1744883753008</updated>
    </task>
    <task id="LOCAL-00068" summary="feat(certificationPartnerController):&quot;adding admin_role to list/show methods&quot;">
      <option name="closed" value="true" />
      <created>1744895581076</created>
      <option name="number" value="00068" />
      <option name="presentableId" value="LOCAL-00068" />
      <option name="project" value="LOCAL" />
      <updated>1744895581076</updated>
    </task>
    <task id="LOCAL-00069" summary="fix(certificationPartnerController):&quot;update urls method&quot;">
      <option name="closed" value="true" />
      <created>1744899795863</created>
      <option name="number" value="00069" />
      <option name="presentableId" value="LOCAL-00069" />
      <option name="project" value="LOCAL" />
      <updated>1744899795863</updated>
    </task>
    <task id="LOCAL-00070" summary="feat(trainAI):&quot;dialog train ai / precision gauge componenet&quot;">
      <option name="closed" value="true" />
      <created>1746541995082</created>
      <option name="number" value="00070" />
      <option name="presentableId" value="LOCAL-00070" />
      <option name="project" value="LOCAL" />
      <updated>1746541995083</updated>
    </task>
    <task id="LOCAL-00071" summary="feat(trainAI): criteria in back (commented)">
      <option name="closed" value="true" />
      <created>1746542147618</created>
      <option name="number" value="00071" />
      <option name="presentableId" value="LOCAL-00071" />
      <option name="project" value="LOCAL" />
      <updated>1746542147618</updated>
    </task>
    <task id="LOCAL-00072" summary="feat(trainAI):&quot;dialog train ai / precision gauge componenet&quot;">
      <option name="closed" value="true" />
      <created>1746542318099</created>
      <option name="number" value="00072" />
      <option name="presentableId" value="LOCAL-00072" />
      <option name="project" value="LOCAL" />
      <updated>1746542318100</updated>
    </task>
    <task id="LOCAL-00073" summary="feat(Audit): load titles from the back + loader">
      <option name="closed" value="true" />
      <created>1746798024157</created>
      <option name="number" value="00073" />
      <option name="presentableId" value="LOCAL-00073" />
      <option name="project" value="LOCAL" />
      <updated>1746798024157</updated>
    </task>
    <task id="LOCAL-00074" summary="feat(trainAI):&quot;create AiAPIService&quot;">
      <option name="closed" value="true" />
      <created>1746802180978</created>
      <option name="number" value="00074" />
      <option name="presentableId" value="LOCAL-00074" />
      <option name="project" value="LOCAL" />
      <updated>1746802180978</updated>
    </task>
    <task id="LOCAL-00075" summary="feat(trainAI):&quot;create AiAPIService&quot;">
      <option name="closed" value="true" />
      <created>1747301735937</created>
      <option name="number" value="00075" />
      <option name="presentableId" value="LOCAL-00075" />
      <option name="project" value="LOCAL" />
      <updated>1747301735937</updated>
    </task>
    <task id="LOCAL-00076" summary="feat(trainAI):&quot;adding arrayApi call / using baseApi Service in AiApiService&quot;">
      <option name="closed" value="true" />
      <created>1747385348067</created>
      <option name="number" value="00076" />
      <option name="presentableId" value="LOCAL-00076" />
      <option name="project" value="LOCAL" />
      <updated>1747385348068</updated>
    </task>
    <task id="LOCAL-00077" summary="feat(trainAI):&quot; adding auth to n8n&quot;">
      <option name="closed" value="true" />
      <created>1747664670861</created>
      <option name="number" value="00077" />
      <option name="presentableId" value="LOCAL-00077" />
      <option name="project" value="LOCAL" />
      <updated>1747664670861</updated>
    </task>
    <task id="LOCAL-00078" summary="feat(trainAI):&quot; showing justification tooltip&quot;">
      <option name="closed" value="true" />
      <created>1747664944852</created>
      <option name="number" value="00078" />
      <option name="presentableId" value="LOCAL-00078" />
      <option name="project" value="LOCAL" />
      <updated>1747664944852</updated>
    </task>
    <task id="LOCAL-00079" summary="feat(trainAI):&quot; remove ngModal and stock if manual title&quot;">
      <option name="closed" value="true" />
      <created>1747665089545</created>
      <option name="number" value="00079" />
      <option name="presentableId" value="LOCAL-00079" />
      <option name="project" value="LOCAL" />
      <updated>1747665089545</updated>
    </task>
    <task id="LOCAL-00080" summary="feat(trainAI):&quot;mise ne prod &quot;">
      <option name="closed" value="true" />
      <created>1747749140862</created>
      <option name="number" value="00080" />
      <option name="presentableId" value="LOCAL-00080" />
      <option name="project" value="LOCAL" />
      <updated>1747749140862</updated>
    </task>
    <task id="LOCAL-00081" summary="feat(certification-partner-trainings):&quot;copy training name&quot;">
      <option name="closed" value="true" />
      <created>1747752211641</created>
      <option name="number" value="00081" />
      <option name="presentableId" value="LOCAL-00081" />
      <option name="project" value="LOCAL" />
      <updated>1747752211641</updated>
    </task>
    <task id="LOCAL-00082" summary="fix(certification-partner-trainings):&quot;oops forgot tooltip&quot;">
      <option name="closed" value="true" />
      <created>1747752436335</created>
      <option name="number" value="00082" />
      <option name="presentableId" value="LOCAL-00082" />
      <option name="project" value="LOCAL" />
      <updated>1747752436335</updated>
    </task>
    <task id="LOCAL-00083" summary="feat(image)">
      <option name="closed" value="true" />
      <created>1747838754589</created>
      <option name="number" value="00083" />
      <option name="presentableId" value="LOCAL-00083" />
      <option name="project" value="LOCAL" />
      <updated>1747838754589</updated>
    </task>
    <task id="LOCAL-00084" summary="feat(audit.md): section IA dans">
      <option name="closed" value="true" />
      <created>1747839565889</created>
      <option name="number" value="00084" />
      <option name="presentableId" value="LOCAL-00084" />
      <option name="project" value="LOCAL" />
      <updated>1747839565889</updated>
    </task>
    <task id="LOCAL-00085" summary="fix(audit.md): remove deprecated error message">
      <option name="closed" value="true" />
      <created>1749039992072</created>
      <option name="number" value="00085" />
      <option name="presentableId" value="LOCAL-00085" />
      <option name="project" value="LOCAL" />
      <updated>1749039992072</updated>
    </task>
    <task id="LOCAL-00086" summary="feat(azureAiDocumentService):consume api SocialSecurityCard">
      <option name="closed" value="true" />
      <created>1750318953816</created>
      <option name="number" value="00086" />
      <option name="presentableId" value="LOCAL-00086" />
      <option name="project" value="LOCAL" />
      <updated>1750318953816</updated>
    </task>
    <task id="LOCAL-00087" summary="feat(processusMetier): service + endpoint">
      <option name="closed" value="true" />
      <created>1750319202181</created>
      <option name="number" value="00087" />
      <option name="presentableId" value="LOCAL-00087" />
      <option name="project" value="LOCAL" />
      <updated>1750319202181</updated>
    </task>
    <task id="LOCAL-00088" summary="feat(workflow-runs-card)">
      <option name="closed" value="true" />
      <created>1750319945378</created>
      <option name="number" value="00088" />
      <option name="presentableId" value="LOCAL-00088" />
      <option name="project" value="LOCAL" />
      <updated>1750319945379</updated>
    </task>
    <task id="LOCAL-00089" summary="feat(workflow-runs-card): adding to cf &amp; rf sides">
      <option name="closed" value="true" />
      <created>1750320102497</created>
      <option name="number" value="00089" />
      <option name="presentableId" value="LOCAL-00089" />
      <option name="project" value="LOCAL" />
      <updated>1750320102497</updated>
    </task>
    <task id="LOCAL-00090" summary="feat(activepieces app): adding link and apikey">
      <option name="closed" value="true" />
      <created>1750320163810</created>
      <option name="number" value="00090" />
      <option name="presentableId" value="LOCAL-00090" />
      <option name="project" value="LOCAL" />
      <updated>1750320163810</updated>
    </task>
    <task id="LOCAL-00091" summary="feat(activepieces &amp; processusMetier app): save / delete metadata info">
      <option name="closed" value="true" />
      <created>1750320230658</created>
      <option name="number" value="00091" />
      <option name="presentableId" value="LOCAL-00091" />
      <option name="project" value="LOCAL" />
      <updated>1750320230658</updated>
    </task>
    <task id="LOCAL-00092" summary="feat(processusMetier): removing unused functions">
      <option name="closed" value="true" />
      <created>1750327972533</created>
      <option name="number" value="00092" />
      <option name="presentableId" value="LOCAL-00092" />
      <option name="project" value="LOCAL" />
      <updated>1750327972533</updated>
    </task>
    <task id="LOCAL-00093" summary="fix(processusMetier): title update">
      <option name="closed" value="true" />
      <created>1750950019744</created>
      <option name="number" value="00093" />
      <option name="presentableId" value="LOCAL-00093" />
      <option name="project" value="LOCAL" />
      <updated>1750950019744</updated>
    </task>
    <task id="LOCAL-00094" summary="fix(processusMetier): clear cache">
      <option name="closed" value="true" />
      <created>1751010267564</created>
      <option name="number" value="00094" />
      <option name="presentableId" value="LOCAL-00094" />
      <option name="project" value="LOCAL" />
      <updated>1751010267564</updated>
    </task>
    <task id="LOCAL-00095" summary="feat(processusMetier): refacto &amp; using entrypoint">
      <option name="closed" value="true" />
      <created>1751023961409</created>
      <option name="number" value="00095" />
      <option name="presentableId" value="LOCAL-00095" />
      <option name="project" value="LOCAL" />
      <updated>1751023961409</updated>
    </task>
    <task id="LOCAL-00096" summary="fix(T8516): desoleee">
      <option name="closed" value="true" />
      <created>1751030277635</created>
      <option name="number" value="00096" />
      <option name="presentableId" value="LOCAL-00096" />
      <option name="project" value="LOCAL" />
      <updated>1751030277636</updated>
    </task>
    <option name="localTasksCounter" value="97" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnknownFeatures">
    <option featureType="com.intellij.fileTypeFactory" implementationName="*.php" />
    <option featureType="com.intellij.fileTypeFactory" implementationName=".env" />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
        <option value="GitHub.CommitStatus" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="RECENT_FILTERS">
      <map>
        <entry key="User">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="*" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="COLUMN_ID_WIDTH">
                <map>
                  <entry key="Table.Default.Author.ColumnIdWidth" value="-1" />
                  <entry key="Table.Default.Date.ColumnIdWidth" value="48" />
                  <entry key="Table.GitHub.CommitStatus.ColumnIdWidth" value="48" />
                  <entry key="Table.Space.CommitStatus.ColumnIdWidth" value="37" />
                </map>
              </option>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="master" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="feat(Audit): load titles from the back + loader" />
    <MESSAGE value="feat(trainAI):&quot;create AiAPIService&quot;" />
    <MESSAGE value="feat(trainAI):&quot;adding arrayApi call / using baseApi Service in AiApiService&quot;" />
    <MESSAGE value="feat(trainAI):&quot; adding auth to n8n&quot;" />
    <MESSAGE value="feat(trainAI):&quot; showing justification tooltip&quot;" />
    <MESSAGE value="feat(trainAI):&quot; remove ngModal and stock if manual title&quot;" />
    <MESSAGE value="feat(trainAI):&quot;mise ne prod &quot;" />
    <MESSAGE value="feat(certification-partner-trainings):&quot;copy training name&quot;" />
    <MESSAGE value="fix(certification-partner-trainings):&quot;oops forgot tooltip&quot;" />
    <MESSAGE value="feat(image)" />
    <MESSAGE value="feat(audit.md ): section IA dans" />
    <MESSAGE value="feat(audit.md): section IA dans" />
    <MESSAGE value="fix(audit.md): remove deprecated error message" />
    <MESSAGE value="feat(azureAiDocumentService): consume api SocialSecurityCard" />
    <MESSAGE value="feat(azureAiDocumentService):consume api SocialSecurityCard" />
    <MESSAGE value="feat(processusMetier): service + endpoint" />
    <MESSAGE value="feat(workflow-runs-card)" />
    <MESSAGE value="feat(workflow-runs-card): adding to cf &amp; rf sides" />
    <MESSAGE value="feat(activepieces app): adding link and apikey" />
    <MESSAGE value="feat(activepieces &amp; processusMetier app): save / delete metadata info" />
    <MESSAGE value="feat(processusMetier): removing unused functions" />
    <MESSAGE value="fix(processusMetier): title update" />
    <MESSAGE value="fix(processusMetier): clear cache" />
    <MESSAGE value="feat(processusMetier): refacto &amp; using entrypoint" />
    <MESSAGE value="fix(T8516): desoleee" />
    <option name="LAST_COMMIT_MESSAGE" value="fix(T8516): desoleee" />
  </component>
  <component name="XDebuggerManager">
    <watches-manager>
      <configuration name="index.php">
        <watch expression="                    // Préparer les titres à évaluer&#10;" />
      </configuration>
    </watches-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>