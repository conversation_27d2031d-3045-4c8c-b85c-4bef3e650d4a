import {ChangeDetectorRef, Component, OnInit} from '@angular/core';
import {OrganismApplication} from '../../../shared/api/models/organism-application';
import {Select, Store} from '@ngxs/store';
import {combineLatest, Observable, Subject} from 'rxjs';
import {takeUntil} from 'rxjs/operators';
import {OrganismState} from '../../../shared/api/state/organism.state';
import {Organism} from '../../../shared/api/models/organism';
import {OrganismApplicationService} from '../../../shared/api/services/organism-application.service';
import {ApplicationsService} from '../../shared/applications.service';
import {HttpClient} from '@angular/common/http';
import {DomSanitizer, SafeResourceUrl} from '@angular/platform-browser';
import {SubscriptionState} from '../../../shared/api/state/subscription.state';
import {ActivatedRoute} from '@angular/router';

interface WorkflowApplicationAuth {
    jwt: string;
}

@Component({
    selector: 'workflow-frame',
    templateUrl: './workflow.frame.component.html',
    styleUrls: ['./workflow.frame.component.scss']
})
export class WorkflowFrameComponent implements OnInit {

    organism: Organism = null;
    workflowApplication: OrganismApplication = null;
    workflowApplicationAuth: WorkflowApplicationAuth = null;
    displayShowEnableApp: boolean = null;
    displayShowTakeSubscription: boolean = null;

    @Select(OrganismState.organism) organism$: Observable<Organism>;
    private _unsubscribeAll = new Subject<void>();

    constructor(
        private _store: Store,
        private ref: ChangeDetectorRef,
        private httpClient: HttpClient,
        private sanitizer: DomSanitizer,
        private _activatedRoute: ActivatedRoute,
        private _applicationsService: ApplicationsService,
        private _organismApplicationService: OrganismApplicationService
    ) {
    }

    ngOnInit(): void {
        combineLatest([
            this.organism$,
            this._organismApplicationService.findAll(),
            this._store.selectOnce(SubscriptionState.subscription)
        ]).pipe(takeUntil(this._unsubscribeAll)).subscribe(([organism, organismApplications, subscription]) => {
            this.organism = organism;
            this.workflowApplication = organismApplications.find(orgApp => orgApp.appId === 'workflow');
            if (this.workflowApplication && this.workflowApplication.enabled) {
                this._organismApplicationService.postData(this.workflowApplication.appId, 'auth', {}).subscribe((workflowApplicationAuth: WorkflowApplicationAuth) => {
                    console.log('WorkflowFrame - Received auth response:', workflowApplicationAuth);
                    if (workflowApplicationAuth?.jwt) {
                        // Debug: Decode JWT to see what's inside with enhanced validation
                        try {
                            const parts = workflowApplicationAuth.jwt.split('.');
                            if (parts.length === 3) {
                                const payload = JSON.parse(atob(parts[1].replace(/-/g, '+').replace(/_/g, '/')));
                                console.log('WorkflowFrame - JWT payload:', payload);

                                // Validate essential fields
                                if (!payload.token) {
                                    console.warn('WorkflowFrame - JWT missing token field');
                                }
                                if (!payload.firstName && !payload.lastName) {
                                    console.warn('WorkflowFrame - JWT missing user name fields');
                                }
                            } else {
                                console.error('WorkflowFrame - Invalid JWT format, expected 3 parts, got:', parts.length);
                            }
                        } catch (e) {
                            console.error('WorkflowFrame - Error decoding JWT:', e);
                            console.error('WorkflowFrame - Problematic JWT:', workflowApplicationAuth.jwt);
                        }
                    } else {
                        console.error('WorkflowFrame - No JWT received from backend');
                    }
                    this.workflowApplicationAuth = workflowApplicationAuth;
                    this.displayShowEnableApp = false;
                    this.displayShowTakeSubscription = false;
                });
            } else if (this.workflowApplication && subscription.allowedApps.includes(this.workflowApplication.appId)) {
                // display enable app
                this.displayShowEnableApp = true;
                this.displayShowTakeSubscription = false;
            } else {
                // display subscribe
                this.displayShowEnableApp = false;
                this.displayShowTakeSubscription = true;
            }
        });
    }

    getFrameUrl(): SafeResourceUrl {
        this.ref.detach(); // no refresh
        const workflowPath = history.state.workflowPath ?? null;
        const url = new URL('/sign-in.html', 'https://' + this.organism.subDomain + '.processus.wedof.fr');
        url.searchParams.append('jwt', this.workflowApplicationAuth.jwt);
        if (workflowPath) {
            url.searchParams.append('path', workflowPath);
        }
        console.log('WorkflowFrame - Generated iframe URL:', url.toString());
        return this.sanitizer.bypassSecurityTrustResourceUrl(url.toString());
    }
}
